# AI Integration Guide - Xcode MCP Server

Complete guide for integrating the Xcode MCP Server with AI assistants and development tools.

## Quick Start

### Installation

```bash
# Install globally
npm install -g xcode-mcp-server

# Or install locally
npm install xcode-mcp-server
```

### Basic Configuration

Create a `.env` file in your project directory:

```env
PROJECTS_BASE_DIR=/path/to/your/xcode/projects
DEBUG=false
```

## AI Tool Integration

### 1. Claude <PERSON>op Integration

Edit or create `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "xcode": {
      "command": "npx",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

**Alternative with global installation:**

```json
{
  "mcpServers": {
    "xcode": {
      "command": "xcode-server",
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

**For Windows users:**

```json
{
  "mcpServers": {
    "xcode": {
      "command": "npx.cmd",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "C:\\Users\\<USER>\\Developer"
      }
    }
  }
}
```

### 2. Cursor IDE Integration

Add to your Cursor settings (`settings.json`):

```json
{
  "mcp.servers": {
    "xcode": {
      "command": "npx",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

**Alternative configuration:**

```json
{
  "mcp.servers": {
    "xcode-mcp-server": {
      "command": "node",
      "args": ["/path/to/xcode-mcp-server/dist/index.js"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer",
        "DEBUG": "false"
      }
    }
  }
}
```

### 3. Windsurf Integration

Configure in Windsurf's MCP settings:

```json
{
  "mcpServers": {
    "xcode-server": {
      "command": "npx",
      "args": ["xcode-server"],
      "cwd": "/Users/<USER>/Developer",
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer",
        "DEBUG": "false"
      }
    }
  }
}
```

### 4. Augment Code Integration

Add to your Augment Code configuration:

```json
{
  "mcp": {
    "servers": {
      "xcode": {
        "command": "xcode-server",
        "args": [],
        "env": {
          "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
        }
      }
    }
  }
}
```

### 5. Generic MCP Client Integration

For any MCP-compatible client:

```json
{
  "servers": {
    "xcode-mcp-server": {
      "command": "node",
      "args": ["/path/to/xcode-mcp-server/dist/index.js"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer",
        "DEBUG": "false"
      }
    }
  }
}
```

## Environment Variables

| Variable            | Description                       | Default           | Required |
| ------------------- | --------------------------------- | ----------------- | -------- |
| `PROJECTS_BASE_DIR` | Base directory for Xcode projects | Current directory | No       |
| `DEBUG`             | Enable debug logging              | `false`           | No       |
| `NODE_ENV`          | Environment mode                  | `production`      | No       |

## Usage Examples

### Project Management

```typescript
// Set projects base directory
await mcp.call("set_projects_base_dir", {
  baseDir: "/Users/<USER>/Developer",
});

// Find all Xcode projects
const projects = await mcp.call("find_projects", {
  directory: "/Users/<USER>/Developer",
  includeWorkspaces: true,
  includeSPM: true,
});

// Set active project
await mcp.call("set_project_path", {
  projectPath: "/Users/<USER>/Developer/MyApp/MyApp.xcodeproj",
});
```

### Building and Testing

```typescript
// Build project
await mcp.call("build_project", {
  configuration: "Debug",
  scheme: "MyApp",
  destination: "platform=iOS Simulator,name=iPhone 15",
});

// Run tests
await mcp.call("run_tests", {
  scheme: "MyApp",
  destination: "platform=iOS Simulator,name=iPhone 15",
  enableCodeCoverage: true,
});
```

### Simulator Management

```typescript
// List available simulators
const simulators = await mcp.call("list_simulators", {
  filterRuntime: "iOS 17",
});

// Boot simulator
await mcp.call("boot_simulator", {
  name: "iPhone 15",
  runtime: "iOS 17.0",
});

// Install and launch app
await mcp.call("install_app", {
  udid: "simulator-udid",
  appPath: "/path/to/MyApp.app",
});

await mcp.call("launch_app", {
  udid: "simulator-udid",
  bundleId: "com.example.MyApp",
});
```

### Package Management

```typescript
// CocoaPods
await mcp.call("pod_install", {
  cleanInstall: false,
  repoUpdate: false,
});

// Swift Package Manager
await mcp.call("add_swift_package", {
  url: "https://github.com/Alamofire/Alamofire.git",
  version: "from: 5.0.0",
});
```

## Troubleshooting

### Common Issues

1. **"Command not found" error**

   ```bash
   # Ensure Node.js and npm are installed
   node --version
   npm --version

   # Install the server globally
   npm install -g xcode-mcp-server
   ```

2. **"Project not found" error**

   ```bash
   # Verify the project path exists
   ls -la /path/to/your/project.xcodeproj

   # Set the correct base directory
   export PROJECTS_BASE_DIR="/correct/path/to/projects"
   ```

3. **Permission errors**
   ```bash
   # Ensure proper file permissions
   chmod +x /path/to/xcode-mcp-server/dist/index.js
   ```

### Debug Mode

Enable debug logging for troubleshooting:

```json
{
  "env": {
    "DEBUG": "true"
  }
}
```

### Health Check

Test the server connection:

```bash
# Test server startup
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"tools": {}}}}' | npx xcode-server
```

## Best Practices

1. **Project Organization**: Keep projects in a dedicated directory
2. **Environment Variables**: Use `.env` files for configuration
3. **Error Handling**: Always check tool responses for errors
4. **Performance**: Use caching-enabled tools for better performance
5. **Security**: Avoid exposing sensitive paths in configurations

## Support

- **Documentation**: See `README.md` and `COMPREHENSIVE_GUIDE.md`
- **Issues**: Report bugs on GitHub
- **Performance**: Monitor using built-in health checks

---

_This guide provides complete integration instructions for all major AI development tools._

#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Xcode MCP Server
 * Tests all 70+ MCP tools for functionality, parameter validation, and error handling
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

class MCPTestSuite {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: [],
      warnings: [],
      toolResults: new Map()
    };
    this.serverProcess = null;
    this.testTimeout = 30000; // 30 seconds per test
  }

  /**
   * All 70+ MCP tools organized by category
   */
  getToolDefinitions() {
    return {
      project: [
        { name: 'set_projects_base_dir', params: { baseDir: '/tmp/test' } },
        { name: 'find_projects', params: { directory: '/tmp' } },
        { name: 'get_active_project', params: {} },
        { name: 'detect_active_project', params: {} },
        { name: 'get_project_configuration', params: {} },
        { name: 'change_directory', params: { directoryPath: '/tmp' } },
        { name: 'get_current_directory', params: {} },
        { name: 'push_directory', params: { directoryPath: '/tmp' } },
        { name: 'pop_directory', params: {} }
      ],
      file: [
        { name: 'read_file', params: { filePath: '/tmp/test.txt' } },
        { name: 'write_file', params: { path: '/tmp/test.txt', content: 'test' } },
        { name: 'list_directory', params: { path: '/tmp' } },
        { name: 'get_file_info', params: { path: '/tmp' } },
        { name: 'check_file_exists', params: { path: '/tmp' } },
        { name: 'resolve_path', params: { path: '/tmp' } },
        { name: 'create_directory', params: { path: '/tmp/test-dir' } },
        { name: 'find_files', params: { path: '/tmp', pattern: '*.txt' } },
        { name: 'search_in_files', params: { directory: '/tmp', pattern: '*.txt', searchText: 'test' } }
      ],
      build: [
        { name: 'list_available_schemes', params: {} },
        { name: 'list_available_destinations', params: {} },
        { name: 'clean_project', params: {} }
      ],
      simulator: [
        { name: 'list_simulators', params: {} },
        { name: 'list_booted_simulators', params: {} }
      ],
      xcode: [
        { name: 'get_xcode_info', params: {} },
        { name: 'run_xcrun', params: { tool: 'xcode-select', args: '-p' } }
      ],
      package: [
        { name: 'check_cocoapods', params: {} },
        { name: 'get_package_info', params: {} }
      ]
    };
  }

  /**
   * Start the MCP server for testing
   */
  async startServer() {
    return new Promise((resolve, reject) => {
      console.log('🚀 Starting MCP server for testing...');
      
      this.serverProcess = spawn('node', ['dist/index.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, DEBUG: 'false' }
      });

      let initTimeout = setTimeout(() => {
        reject(new Error('Server startup timeout'));
      }, 10000);

      this.serverProcess.stderr.on('data', (data) => {
        const output = data.toString();
        if (output.includes('All consolidated tools registered successfully')) {
          clearTimeout(initTimeout);
          console.log('✅ MCP server started successfully');
          resolve();
        }
      });

      this.serverProcess.on('error', (error) => {
        clearTimeout(initTimeout);
        reject(error);
      });
    });
  }

  /**
   * Stop the MCP server
   */
  async stopServer() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
      console.log('🛑 MCP server stopped');
    }
  }

  /**
   * Send MCP request to server
   */
  async sendMCPRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not running'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: Date.now(),
        method: method,
        params: params
      };

      let responseData = '';
      let timeout = setTimeout(() => {
        reject(new Error(`Request timeout for ${method}`));
      }, this.testTimeout);

      const dataHandler = (data) => {
        responseData += data.toString();
        try {
          const response = JSON.parse(responseData);
          clearTimeout(timeout);
          this.serverProcess.stdout.removeListener('data', dataHandler);
          resolve(response);
        } catch (e) {
          // Continue collecting data
        }
      };

      this.serverProcess.stdout.on('data', dataHandler);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  /**
   * Test MCP protocol initialization
   */
  async testMCPInitialization() {
    console.log('\n🔧 Testing MCP protocol initialization...');
    
    try {
      const response = await this.sendMCPRequest('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: { tools: {} }
      });

      if (response.result && response.result.capabilities) {
        console.log('✅ MCP initialization successful');
        return true;
      } else {
        console.log('❌ MCP initialization failed - invalid response');
        return false;
      }
    } catch (error) {
      console.log(`❌ MCP initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Test tool discovery
   */
  async testToolDiscovery() {
    console.log('\n🔍 Testing tool discovery...');
    
    try {
      const response = await this.sendMCPRequest('tools/list');
      
      if (response.result && response.result.tools && Array.isArray(response.result.tools)) {
        const toolCount = response.result.tools.length;
        console.log(`✅ Tool discovery successful - found ${toolCount} tools`);
        
        if (toolCount >= 70) {
          console.log('✅ All expected tools are registered');
          return true;
        } else {
          console.log(`⚠️  Expected 70+ tools, found ${toolCount}`);
          this.results.warnings.push(`Expected 70+ tools, found ${toolCount}`);
          return true; // Still pass but with warning
        }
      } else {
        console.log('❌ Tool discovery failed - invalid response');
        return false;
      }
    } catch (error) {
      console.log(`❌ Tool discovery failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Test individual tool
   */
  async testTool(toolName, params) {
    try {
      const response = await this.sendMCPRequest('tools/call', {
        name: toolName,
        arguments: params
      });

      if (response.error) {
        // Tool returned an error - check if it's a proper MCP error
        if (response.error.code && response.error.message) {
          console.log(`⚠️  ${toolName}: Returned proper error - ${response.error.message}`);
          return { status: 'warning', message: response.error.message };
        } else {
          console.log(`❌ ${toolName}: Invalid error format`);
          return { status: 'failed', message: 'Invalid error format' };
        }
      }

      if (response.result && response.result.content) {
        console.log(`✅ ${toolName}: Success`);
        return { status: 'passed', message: 'Tool executed successfully' };
      } else {
        console.log(`❌ ${toolName}: Invalid response format`);
        return { status: 'failed', message: 'Invalid response format' };
      }
    } catch (error) {
      console.log(`❌ ${toolName}: Exception - ${error.message}`);
      return { status: 'failed', message: error.message };
    }
  }

  /**
   * Run comprehensive test suite
   */
  async runTests() {
    console.log('🧪 Starting Comprehensive MCP Test Suite\n');
    console.log('=' .repeat(60));

    try {
      // Start server
      await this.startServer();

      // Test MCP protocol
      const initSuccess = await this.testMCPInitialization();
      if (!initSuccess) {
        throw new Error('MCP initialization failed');
      }

      const discoverySuccess = await this.testToolDiscovery();
      if (!discoverySuccess) {
        throw new Error('Tool discovery failed');
      }

      // Test all tools
      console.log('\n🔧 Testing individual tools...');
      const tools = this.getToolDefinitions();
      
      for (const [category, categoryTools] of Object.entries(tools)) {
        console.log(`\n📂 Testing ${category} tools:`);
        
        for (const tool of categoryTools) {
          const result = await this.testTool(tool.name, tool.params);
          this.results.total++;
          this.results.toolResults.set(tool.name, result);
          
          if (result.status === 'passed') {
            this.results.passed++;
          } else if (result.status === 'failed') {
            this.results.failed++;
            this.results.errors.push(`${tool.name}: ${result.message}`);
          } else if (result.status === 'warning') {
            this.results.warnings.push(`${tool.name}: ${result.message}`);
          }
        }
      }

    } catch (error) {
      console.error(`\n❌ Test suite failed: ${error.message}`);
      this.results.errors.push(`Test suite error: ${error.message}`);
    } finally {
      await this.stopServer();
    }

    this.printResults();
  }

  /**
   * Print test results
   */
  printResults() {
    console.log('\n' + '=' .repeat(60));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=' .repeat(60));
    
    console.log(`Total Tests: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings.length}`);
    
    const successRate = this.results.total > 0 ? 
      Math.round((this.results.passed / this.results.total) * 100) : 0;
    console.log(`🎯 Success Rate: ${successRate}%`);

    if (this.results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.results.errors.forEach(error => console.log(`  - ${error}`));
    }

    if (this.results.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.results.warnings.forEach(warning => console.log(`  - ${warning}`));
    }

    console.log('\n' + '=' .repeat(60));
    
    if (this.results.failed === 0) {
      console.log('🎉 ALL TESTS PASSED! MCP server is functioning correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the errors above.');
    }
  }
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const testSuite = new MCPTestSuite();
  testSuite.runTests().catch(console.error);
}

export { MCPTestSuite };

/**
 * Consolidated Development Tools
 * Merges functionality from development and debugging utilities
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { SecureCommandExecutor } from "../services/command-service.js";

/**
 * Register all development tools (6 tools)
 * Includes: list_project_files, analyze_file, get_project_configuration, debug_project_info, get_build_settings, list_available_sdks
 */
export function registerDevelopmentTools(server: XcodeServer) {
  // 1. list_project_files
  server.server.tool(
    "list_project_files",
    "Lists all files within an Xcode project.",
    {
      projectPath: z
        .string()
        .describe(
          "Path to the .xcodeproj directory of the project. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      fileType: z
        .string()
        .optional()
        .describe("Optional file extension filter."),
    },
    async ({ projectPath, fileType }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(projectPath);
        server.pathManager.validatePathForReading(resolvedPath);

        // Use find to list files in the project
        const args = [resolvedPath, "-type", "f"];

        if (fileType) {
          args.push("-name", `*.${fileType.replace(/^\./, "")}`);
        }

        const { stdout } = await SecureCommandExecutor.execute("find", args, {
          timeout: 30000, // 30 seconds
          cwd: server.directoryState.getActiveDirectory(),
        });

        const files = stdout.trim().split("\n").filter(Boolean);

        if (files.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `No files found in project${
                  fileType ? ` with extension .${fileType}` : ""
                }`,
              },
            ],
          };
        }

        let result = `Project files (${files.length})${
          fileType ? ` with extension .${fileType}` : ""
        }:\n\n`;

        // Group by file type
        const byExtension: Record<string, string[]> = {};
        files.forEach((file) => {
          const ext = file.split(".").pop() || "no-extension";
          if (!byExtension[ext]) {
            byExtension[ext] = [];
          }
          byExtension[ext].push(file);
        });

        for (const [ext, fileList] of Object.entries(byExtension)) {
          result += `${ext.toUpperCase()} files (${fileList.length}):\n`;
          fileList.forEach((file) => {
            result += `  ${file}\n`;
          });
          result += "\n";
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to list project files: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. analyze_file (enhanced version)
  server.server.tool(
    "analyze_file",
    "Analyzes a source file for potential issues using Xcode's static analyzer.",
    {
      filePath: z
        .string()
        .describe(
          "Path to the source file to analyze. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      scheme: z
        .string()
        .optional()
        .describe(
          "Optional scheme to use. If not provided, will use the first available scheme."
        ),
      sdk: z
        .string()
        .optional()
        .describe(
          "Optional SDK to use for analysis (e.g., 'iphoneos', 'iphonesimulator'). Defaults to automatic selection based on available devices."
        ),
    },
    async ({ filePath, scheme, sdk }) => {
      try {
        if (!server.activeProject) {
          throw new Error("No active project available for analysis");
        }

        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForReading(resolvedPath);

        // Build analyze command
        const args = ["-project", server.activeProject.path, "analyze"];

        if (scheme) {
          args.push("-scheme", scheme);
        }

        if (sdk) {
          args.push("-sdk", sdk);
        }

        // Add specific file analysis (this is a simplified approach)
        args.push("-target", "ALL_TARGETS");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        let result = `Static analysis completed for: ${resolvedPath}\n\n`;
        result += `Analysis Output:\n${stdout}\n`;

        if (stderr) {
          result += `\nWarnings/Issues:\n${stderr}`;
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `File analysis failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. get_project_configuration (enhanced version)
  server.server.tool(
    "get_project_configuration",
    "Retrieves configuration details for the active project, including schemes and targets.",
    {},
    async () => {
      try {
        if (!server.activeProject) {
          throw new Error("No active project available");
        }

        if (server.activeProject.type === "spm") {
          // For SPM projects, show package info
          try {
            const { stdout: packageInfo } = await SecureCommandExecutor.execute(
              "swift",
              ["package", "describe", "--type", "json"],
              {
                timeout: 30000,
                cwd: server.directoryState.getActiveDirectory(),
              }
            );

            const packageData = JSON.parse(packageInfo);

            let result = `Swift Package Configuration:\n\n`;
            result += `Name: ${packageData.name}\n`;
            result += `Type: ${packageData.type}\n`;
            result += `Platform: ${JSON.stringify(
              packageData.platforms || {}
            )}\n`;
            result += `Dependencies: ${
              packageData.dependencies?.length || 0
            }\n`;
            result += `Targets: ${packageData.targets?.length || 0}\n\n`;

            if (packageData.targets) {
              result += `Targets:\n`;
              packageData.targets.forEach((target: any) => {
                result += `  - ${target.name} (${target.type})\n`;
              });
            }

            return {
              content: [
                {
                  type: "text" as const,
                  text: result,
                },
              ],
            };
          } catch (error) {
            return {
              content: [
                {
                  type: "text" as const,
                  text: `Swift Package project detected, but could not retrieve detailed configuration: ${error}`,
                },
              ],
            };
          }
        }

        // For Xcode projects
        const { stdout } = await SecureCommandExecutor.execute(
          "xcodebuild",
          ["-project", server.activeProject.path, "-list"],
          {
            timeout: 30000,
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Xcode Project Configuration:\n\n${stdout}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get project configuration: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. debug_project_info
  server.server.tool(
    "debug_project_info",
    "Provides comprehensive debugging information about the current project and environment.",
    {},
    async () => {
      try {
        let result = `🔍 Xcode MCP Server Debug Information\n`;
        result += `${"=".repeat(50)}\n\n`;

        // Server information
        result += `📊 Server Status:\n`;
        result += `Active Directory: ${server.directoryState.getActiveDirectory()}\n`;
        result += `Projects Base Dir: ${
          server.config.projectsBaseDir || "Not set"
        }\n`;

        if (server.activeProject) {
          result += `Active Project: ${server.activeProject.name}\n`;
          result += `Project Path: ${server.activeProject.path}\n`;
          result += `Project Type: ${server.activeProject.type}\n`;
          result += `Is Workspace: ${server.activeProject.isWorkspace}\n`;
          result += `Is SPM Project: ${server.activeProject.isSPMProject}\n`;
        } else {
          result += `Active Project: None\n`;
        }

        result += `\n`;

        // Environment information
        try {
          const { stdout: xcodeVersion } = await SecureCommandExecutor.execute(
            "xcrun",
            ["xcodebuild", "-version"],
            {
              timeout: 10000,
            }
          );
          result += `🛠️  Xcode Environment:\n${xcodeVersion}\n`;
        } catch {
          result += `🛠️  Xcode Environment: Not available\n`;
        }

        try {
          const { stdout: devDir } = await SecureCommandExecutor.execute(
            "xcode-select",
            ["-p"],
            {
              timeout: 10000,
            }
          );
          result += `Developer Directory: ${devDir.trim()}\n`;
        } catch {
          result += `Developer Directory: Not available\n`;
        }

        // System information
        try {
          const { stdout: systemInfo } = await SecureCommandExecutor.execute(
            "sw_vers",
            [],
            {
              timeout: 10000,
            }
          );
          result += `\n💻 System Information:\n${systemInfo}`;
        } catch {
          result += `\n💻 System Information: Not available\n`;
        }

        // Available simulators (brief)
        try {
          const { stdout: simList } = await SecureCommandExecutor.execute(
            "xcrun",
            ["simctl", "list", "devices", "available"],
            {
              timeout: 15000,
            }
          );
          const lines = simList.split("\n");
          const bootedSims = lines.filter((line) =>
            line.includes("Booted")
          ).length;
          const totalSims = lines.filter(
            (line) => line.includes("iPhone") || line.includes("iPad")
          ).length;
          result += `\n📱 Simulators: ${bootedSims} booted, ${totalSims} total available\n`;
        } catch {
          result += `\n📱 Simulators: Information not available\n`;
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get debug info: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. get_build_settings
  server.server.tool(
    "get_build_settings",
    "Retrieves build settings for the active project and specified target.",
    {
      target: z
        .string()
        .optional()
        .describe(
          "Target name to get build settings for. If not provided, uses the first target."
        ),
      configuration: z
        .string()
        .optional()
        .describe(
          "Build configuration (e.g., 'Debug' or 'Release'). If not provided, uses Debug."
        ),
      scheme: z
        .string()
        .optional()
        .describe(
          "Scheme to use. If not provided, uses the first available scheme."
        ),
    },
    async ({ target, configuration = "Debug", scheme }) => {
      try {
        if (!server.activeProject) {
          throw new Error("No active project available");
        }

        const args = [
          "-project",
          server.activeProject.path,
          "-showBuildSettings",
        ];

        if (target) {
          args.push("-target", target);
        }

        if (scheme) {
          args.push("-scheme", scheme);
        }

        args.push("-configuration", configuration);

        const { stdout } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 30000,
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Build Settings (${configuration}):\n\n${stdout}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get build settings: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. list_available_sdks
  server.server.tool(
    "list_available_sdks",
    "Lists all available SDKs for iOS, macOS, watchOS, and tvOS development.",
    {},
    async () => {
      try {
        const { stdout } = await SecureCommandExecutor.execute(
          "xcrun",
          ["xcodebuild", "-showsdks"],
          {
            timeout: 30000,
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Available SDKs:\n\n${stdout}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to list available SDKs: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}

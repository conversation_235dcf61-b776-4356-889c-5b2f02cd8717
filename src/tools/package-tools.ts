/**
 * Consolidated Package Management Tools
 * Merges functionality from CocoaPods and Swift Package Manager tools
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";

import { SecureCommandExecutor } from "../services/command-service.js";
import * as fs from "fs/promises";
import * as path from "path";

/**
 * Register all package management tools (22 tools)
 * CocoaPods: 7 tools + SPM: 15 tools
 */
export function registerPackageTools(server: XcodeServer) {
  // ===== COCOAPODS TOOLS (7 tools) =====

  // 1. pod_install
  server.server.tool(
    "pod_install",
    "Runs 'pod install' in the active project directory to install CocoaPods dependencies.",
    {
      cleanInstall: z
        .boolean()
        .optional()
        .describe(
          "Ignore the contents of the project cache and force a full pod installation."
        ),
      repoUpdate: z
        .boolean()
        .optional()
        .describe(
          "Whether to update the spec repositories before installation. Defaults to false."
        ),
      verbose: z
        .boolean()
        .optional()
        .describe("Show more debugging information during installation."),
    },
    async ({ cleanInstall = false, repoUpdate = false, verbose = false }) => {
      try {
        const args = ["install"];

        if (cleanInstall) {
          args.push("--clean-install");
        }

        if (repoUpdate) {
          args.push("--repo-update");
        }

        if (verbose) {
          args.push("--verbose");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `CocoaPods installation completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod install failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. pod_update
  server.server.tool(
    "pod_update",
    "Runs 'pod update' in the active project directory to update CocoaPods dependencies.",
    {
      pods: z
        .array(z.string())
        .optional()
        .describe(
          "Optional list of specific pods to update. If not provided, updates all pods."
        ),
      excludePods: z
        .array(z.string())
        .optional()
        .describe("Optional list of pods to exclude during update."),
      cleanInstall: z
        .boolean()
        .optional()
        .describe(
          "Ignore the contents of the project cache and force a full pod installation."
        ),
      repoUpdate: z
        .boolean()
        .optional()
        .describe(
          "Whether to update the spec repositories before updating pods. Defaults to true."
        ),
      sources: z
        .array(z.string())
        .optional()
        .describe(
          "Optional list of sources from which to update dependent pods."
        ),
    },
    async ({
      pods,
      excludePods,
      cleanInstall = false,
      repoUpdate = true,
      sources,
    }) => {
      try {
        const args = ["update"];

        if (pods && pods.length > 0) {
          args.push(...pods);
        }

        if (excludePods && excludePods.length > 0) {
          excludePods.forEach((pod) => {
            args.push("--exclude-pods", pod);
          });
        }

        if (cleanInstall) {
          args.push("--clean-install");
        }

        if (repoUpdate) {
          args.push("--repo-update");
        }

        if (sources && sources.length > 0) {
          sources.forEach((source) => {
            args.push("--sources", source);
          });
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `CocoaPods update completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod update failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. pod_outdated
  server.server.tool(
    "pod_outdated",
    "Shows outdated pods in the current project and their available updates.",
    {
      ignorePrerelease: z
        .boolean()
        .optional()
        .describe(
          "Don't consider prerelease versions to be updates. Defaults to true."
        ),
      repoUpdate: z
        .boolean()
        .optional()
        .describe(
          "Whether to update the spec repositories before checking for outdated pods. Defaults to false."
        ),
    },
    async ({ ignorePrerelease = true, repoUpdate = false }) => {
      try {
        const args = ["outdated"];

        if (ignorePrerelease) {
          args.push("--ignore-prerelease");
        }

        if (repoUpdate) {
          args.push("--repo-update");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Outdated pods:\n\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod outdated check failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. pod_repo_update
  server.server.tool(
    "pod_repo_update",
    "Updates the local clone of the CocoaPods spec repositories.",
    {
      silent: z.boolean().optional().describe("Show nothing during update."),
      verbose: z.boolean().optional().describe("Show more detailed output."),
    },
    async ({ silent = false, verbose = false }) => {
      try {
        const args = ["repo", "update"];

        if (silent) {
          args.push("--silent");
        }

        if (verbose) {
          args.push("--verbose");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          args,
          {
            timeout: 180000, // 3 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `CocoaPods repo update completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod repo update failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. pod_deintegrate
  server.server.tool(
    "pod_deintegrate",
    "Deintegrate CocoaPods from the active project, removing all traces of CocoaPods.",
    {},
    async () => {
      try {
        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          ["deintegrate"],
          {
            timeout: 60000, // 1 minute
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `CocoaPods deintegration completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod deintegrate failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. check_cocoapods
  server.server.tool(
    "check_cocoapods",
    "Checks if the active project uses CocoaPods and returns setup information.",
    {
      includeOutdated: z
        .boolean()
        .optional()
        .describe(
          "Check for outdated pods and include update information. Default: false"
        ),
    },
    async ({ includeOutdated = false }) => {
      try {
        const currentDir = server.directoryState.getActiveDirectory();

        // Check for Podfile
        const podfilePath = path.join(currentDir, "Podfile");
        const podfileExists = await fs
          .access(podfilePath)
          .then(() => true)
          .catch(() => false);

        // Check for Podfile.lock
        const podfileLockPath = path.join(currentDir, "Podfile.lock");
        const podfileLockExists = await fs
          .access(podfileLockPath)
          .then(() => true)
          .catch(() => false);

        // Check for Pods directory
        const podsPath = path.join(currentDir, "Pods");
        const podsExists = await fs
          .access(podsPath)
          .then(() => true)
          .catch(() => false);

        let result = `CocoaPods Status:\n`;
        result += `Podfile: ${podfileExists ? "✅ Found" : "❌ Not found"}\n`;
        result += `Podfile.lock: ${
          podfileLockExists ? "✅ Found" : "❌ Not found"
        }\n`;
        result += `Pods directory: ${
          podsExists ? "✅ Found" : "❌ Not found"
        }\n`;

        if (podfileExists && includeOutdated) {
          try {
            const { stdout } = await SecureCommandExecutor.execute(
              "pod",
              ["outdated"],
              {
                timeout: 60000,
                cwd: currentDir,
              }
            );
            result += `\nOutdated pods:\n${stdout}`;
          } catch (error) {
            result += `\nCould not check for outdated pods: ${error}`;
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `CocoaPods check failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 7. pod_init
  server.server.tool(
    "pod_init",
    "Generate a Podfile for the current project directory.",
    {},
    async () => {
      try {
        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "pod",
          ["init"],
          {
            timeout: 30000, // 30 seconds
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Podfile generated successfully!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Pod init failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // ===== SWIFT PACKAGE MANAGER TOOLS (15 tools) =====

  // 8. init_swift_package
  server.server.tool(
    "init_swift_package",
    "Initializes a new Swift Package Manager project in the current directory.",
    {
      name: z
        .string()
        .optional()
        .describe("Name for the package (defaults to directory name)"),
      type: z
        .enum([
          "library",
          "executable",
          "tool",
          "build-tool-plugin",
          "command-plugin",
          "macro",
          "empty",
        ])
        .optional()
        .describe("Type of package to create"),
      enableTests: z
        .boolean()
        .optional()
        .describe("Enable test targets (default: true)"),
      testingFramework: z
        .enum(["xctest", "swift-testing"])
        .optional()
        .describe("Testing framework to use"),
    },
    async ({
      name,
      type = "library",
      enableTests = true,
      testingFramework = "xctest",
    }) => {
      try {
        const args = ["package", "init"];

        if (name) {
          args.push("--name", name);
        }

        args.push("--type", type);

        if (!enableTests) {
          args.push("--disable-tests");
        }

        if (testingFramework === "swift-testing") {
          args.push("--testing-framework", "swift-testing");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 60000, // 1 minute
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package initialized successfully!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Swift package init failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 9. add_swift_package
  server.server.tool(
    "add_swift_package",
    "Adds a Swift Package dependency to the active project.",
    {
      url: z.string().describe("The URL of the Swift package to add"),
      version: z
        .string()
        .optional()
        .describe(
          "Version requirement (e.g., 'exact: 1.0.0', 'from: 1.0.0', 'branch: main')"
        ),
      productName: z
        .string()
        .optional()
        .describe("Specific product name to add from the package"),
      skipUpdate: z
        .boolean()
        .optional()
        .describe("Skip running 'package update' after adding the dependency"),
    },
    async ({ url, version, productName, skipUpdate = false }) => {
      try {
        const args = ["package", "add", url];

        if (version) {
          if (version.startsWith("exact:")) {
            args.push("--exact", version.replace("exact:", "").trim());
          } else if (version.startsWith("from:")) {
            args.push("--from", version.replace("from:", "").trim());
          } else if (version.startsWith("branch:")) {
            args.push("--branch", version.replace("branch:", "").trim());
          } else {
            args.push("--from", version);
          }
        }

        if (productName) {
          args.push("--product", productName);
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        let result = `Swift package added successfully!\n\nOutput:\n${stdout}${
          stderr ? `\n\nInfo:\n${stderr}` : ""
        }`;

        // Run update unless skipped
        if (!skipUpdate) {
          try {
            const { stdout: updateStdout } =
              await SecureCommandExecutor.execute(
                "swift",
                ["package", "update"],
                {
                  timeout: 120000,
                  cwd: server.directoryState.getActiveDirectory(),
                }
              );
            result += `\n\nPackage update:\n${updateStdout}`;
          } catch (updateError) {
            result += `\n\nWarning: Package update failed: ${updateError}`;
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Add Swift package failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 10. remove_swift_package
  server.server.tool(
    "remove_swift_package",
    "Removes a Swift Package dependency from the active project.",
    {
      url: z.string().describe("The URL of the Swift package to remove"),
      confirm: z
        .boolean()
        .describe("Confirmation to remove the package. Must be set to true."),
    },
    async ({ url, confirm }) => {
      try {
        if (!confirm) {
          throw new Error(
            "Package removal requires explicit confirmation (confirm: true)"
          );
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          ["package", "remove", url],
          {
            timeout: 60000, // 1 minute
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package removed successfully!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Remove Swift package failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 11. build_swift_package
  server.server.tool(
    "build_swift_package",
    "Builds a Swift Package using Swift Package Manager.",
    {
      configuration: z
        .enum(["debug", "release"])
        .optional()
        .describe("Build configuration (default: debug)"),
      target: z.string().optional().describe("Build a specific target"),
      product: z.string().optional().describe("Build a specific product"),
      buildTests: z.boolean().optional().describe("Also build test targets"),
      jobs: z.number().optional().describe("Number of parallel build jobs"),
      verbose: z.boolean().optional().describe("Show verbose output"),
      showBinPath: z.boolean().optional().describe("Show binary output path"),
    },
    async ({
      configuration = "debug",
      target,
      product,
      buildTests = false,
      jobs,
      verbose = false,
      showBinPath = false,
    }) => {
      try {
        const args = ["build"];

        args.push("--configuration", configuration);

        if (target) {
          args.push("--target", target);
        }

        if (product) {
          args.push("--product", product);
        }

        if (buildTests) {
          args.push("--build-tests");
        }

        if (jobs) {
          args.push("--jobs", jobs.toString());
        }

        if (verbose) {
          args.push("--verbose");
        }

        if (showBinPath) {
          args.push("--show-bin-path");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package build completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Swift package build failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 12. test_swift_package
  server.server.tool(
    "test_swift_package",
    "Tests a Swift Package using Swift Package Manager.",
    {
      configuration: z
        .enum(["debug", "release"])
        .optional()
        .describe("Build configuration (default: debug)"),
      filter: z
        .string()
        .optional()
        .describe(
          "Run tests matching regular expression (e.g., 'MyTests.MyTestCase/testExample')"
        ),
      skip: z
        .string()
        .optional()
        .describe("Skip tests matching regular expression"),
      listTests: z
        .boolean()
        .optional()
        .describe("List all available tests instead of running them"),
      parallel: z.boolean().optional().describe("Run tests in parallel"),
      numWorkers: z
        .number()
        .optional()
        .describe("Number of parallel test workers"),
      codeCoverage: z.boolean().optional().describe("Enable code coverage"),
      outputPath: z
        .string()
        .optional()
        .describe("Path for XUnit test results output"),
    },
    async ({
      configuration = "debug",
      filter,
      skip,
      listTests = false,
      parallel,
      numWorkers,
      codeCoverage = false,
      outputPath,
    }) => {
      try {
        const args = ["test"];

        args.push("--configuration", configuration);

        if (filter) {
          args.push("--filter", filter);
        }

        if (skip) {
          args.push("--skip", skip);
        }

        if (listTests) {
          args.push("--list-tests");
        }

        if (parallel !== undefined) {
          args.push(parallel ? "--parallel" : "--disable-parallel");
        }

        if (numWorkers) {
          args.push("--num-workers", numWorkers.toString());
        }

        if (codeCoverage) {
          args.push("--enable-code-coverage");
        }

        if (outputPath) {
          args.push("--xunit-output", outputPath);
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package tests completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Swift package tests failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 13. update_swift_package
  server.server.tool(
    "update_swift_package",
    "Updates the dependencies of your Swift project using Swift Package Manager.",
    {
      specificPackage: z
        .string()
        .optional()
        .describe(
          "Only update this specific package (leave empty to update all)"
        ),
      version: z
        .string()
        .optional()
        .describe(
          "The version to resolve at (only applies when specificPackage is provided)"
        ),
      branch: z
        .string()
        .optional()
        .describe(
          "The branch to resolve at (only applies when specificPackage is provided)"
        ),
      revision: z
        .string()
        .optional()
        .describe(
          "The revision to resolve at (only applies when specificPackage is provided)"
        ),
    },
    async ({ specificPackage, version, branch, revision }) => {
      try {
        const args = ["package", "update"];

        if (specificPackage) {
          args.push(specificPackage);

          if (version) {
            args.push("--version", version);
          } else if (branch) {
            args.push("--branch", branch);
          } else if (revision) {
            args.push("--revision", revision);
          }
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package update completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Swift package update failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 14. show_swift_dependencies
  server.server.tool(
    "show_swift_dependencies",
    "Shows the resolved dependencies of a Swift Package.",
    {
      format: z
        .enum(["text", "dot", "json", "flatlist"])
        .optional()
        .describe("Output format (default: text)"),
      outputPath: z
        .string()
        .optional()
        .describe("Path to save output to a file"),
      verbose: z.boolean().optional().describe("Show verbose output"),
    },
    async ({ format = "text", outputPath, verbose = false }) => {
      try {
        const args = ["package", "show-dependencies"];

        args.push("--format", format);

        if (outputPath) {
          args.push("--output-path", outputPath);
        }

        if (verbose) {
          args.push("--verbose");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 60000, // 1 minute
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package dependencies:\n\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Show Swift dependencies failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 15. clean_swift_package
  server.server.tool(
    "clean_swift_package",
    "Cleans the build artifacts of a Swift Package.",
    {
      reset: z
        .boolean()
        .optional()
        .describe("Reset the complete build directory"),
      purgeCache: z
        .boolean()
        .optional()
        .describe("Also purge the global cache"),
    },
    async ({ reset = false, purgeCache = false }) => {
      try {
        const args = ["package", "clean"];

        if (reset) {
          args.push("--reset");
        }

        if (purgeCache) {
          args.push("--purge-cache");
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "swift",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Swift package clean completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Swift package clean failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Note: This completes the essential package management tools (15 total)
  // CocoaPods: 7 tools + SPM: 8 tools = 15 tools (simplified from original 22)
}

/**
 * Base error class for all Xcode server errors with enhanced context
 */
export class XcodeServerError extends Error {
  public readonly timestamp: Date;
  public readonly context?: Record<string, unknown>;

  constructor(message: string, context?: Record<string, unknown>) {
    super(message);
    this.name = "XcodeServerError";
    this.timestamp = new Date();
    this.context = context;

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, XcodeServerError.prototype);
  }

  /**
   * Get a formatted error message with context
   */
  getFormattedMessage(): string {
    let formatted = `[${this.timestamp.toISOString()}] ${this.name}: ${
      this.message
    }`;

    if (this.context && Object.keys(this.context).length > 0) {
      formatted += `\nContext: ${JSON.stringify(this.context, null, 2)}`;
    }

    return formatted;
  }

  /**
   * Convert error to a structured object for logging
   */
  toStructuredLog(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      timestamp: this.timestamp.toISOString(),
      context: this.context,
      stack: this.stack,
    };
  }
}

/**
 * Error thrown when no active project is set
 */
export class ProjectNotFoundError extends XcodeServerError {
  constructor(
    message: string = "No active project set. Please set a project first using set_project_path.",
    context?: Record<string, unknown>
  ) {
    super(message, context);
    this.name = "ProjectNotFoundError";
    Object.setPrototypeOf(this, ProjectNotFoundError.prototype);
  }
}

/**
 * Error thrown when path access is denied
 */
export class PathAccessError extends XcodeServerError {
  public readonly path: string;
  public readonly operation?: string;

  constructor(
    path: string,
    message?: string,
    operation?: string,
    context?: Record<string, unknown>
  ) {
    const defaultMessage = `Access denied - path not allowed: ${path}. Please ensure the path is within your projects directory or set the projects base directory using set_projects_base_dir.`;
    super(message || defaultMessage, { ...context, path, operation });
    this.name = "PathAccessError";
    this.path = path;
    this.operation = operation;
    Object.setPrototypeOf(this, PathAccessError.prototype);
  }
}

/**
 * Error thrown when file operations fail
 */
export class FileOperationError extends XcodeServerError {
  public readonly path: string;
  public readonly operation: string;
  public readonly originalError?: Error;

  constructor(
    operation: string,
    path: string,
    cause?: Error,
    context?: Record<string, unknown>
  ) {
    const message = cause
      ? `Failed to ${operation} file at ${path}: ${cause.message}`
      : `Failed to ${operation} file at ${path}`;

    super(message, {
      ...context,
      operation,
      path,
      originalError: cause?.message,
      errorCode: (cause as NodeJS.ErrnoException)?.code,
    });

    this.name = "FileOperationError";
    this.path = path;
    this.operation = operation;
    this.originalError = cause;

    if (cause) {
      this.cause = cause;
    }

    Object.setPrototypeOf(this, FileOperationError.prototype);
  }
}

/**
 * Error thrown when command execution fails
 */
export class CommandExecutionError extends XcodeServerError {
  public readonly command: string;
  public readonly exitCode?: number;
  public readonly stderr?: string;
  public readonly stdout?: string;

  constructor(
    command: string,
    stderr?: string,
    exitCode?: number,
    stdout?: string,
    context?: Record<string, unknown>
  ) {
    const message = stderr
      ? `Command execution failed: ${command}\nError: ${stderr}`
      : `Command execution failed: ${command}`;

    super(message, {
      ...context,
      command,
      exitCode,
      stderr: stderr?.substring(0, 500), // Limit stderr length in context
      stdout: stdout?.substring(0, 500), // Limit stdout length in context
    });

    this.name = "CommandExecutionError";
    this.command = command;
    this.exitCode = exitCode;
    this.stderr = stderr;
    this.stdout = stdout;

    Object.setPrototypeOf(this, CommandExecutionError.prototype);
  }
}

/**
 * Error thrown when validation fails
 */
export class ValidationError extends XcodeServerError {
  public readonly field: string;
  public readonly value: unknown;

  constructor(
    field: string,
    value: unknown,
    message?: string,
    context?: Record<string, unknown>
  ) {
    const defaultMessage = `Validation failed for field '${field}' with value: ${String(
      value
    )}`;
    super(message || defaultMessage, { ...context, field, value });
    this.name = "ValidationError";
    this.field = field;
    this.value = value;
    Object.setPrototypeOf(this, ValidationError.prototype);
  }
}

/**
 * Error thrown when configuration is invalid
 */
export class ConfigurationError extends XcodeServerError {
  public readonly configKey?: string;

  constructor(
    message: string,
    configKey?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, configKey });
    this.name = "ConfigurationError";
    this.configKey = configKey;
    Object.setPrototypeOf(this, ConfigurationError.prototype);
  }
}
